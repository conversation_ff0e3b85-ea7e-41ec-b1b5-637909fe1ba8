# encoding: utf-8
"""
ChargeProcessor – portable GUI
Startet den (eingefrorenen) sap_orchestrator.exe aus demselben Ordner.
"""
import locale
import os
import subprocess
import sys
import threading
import time
import tkinter as tk
from pathlib import Path
from tkinter import messagebox, ttk
from typing import Optional

# --------------------------------------------------------------------------
# Hilfsfunktionen für portable Pfade
# --------------------------------------------------------------------------
def get_base_dir() -> Path:
    """
    Rückgabe des Ordners, in dem die EXE bzw. das Skript liegt.
    """
    if getattr(sys, "frozen", False):
        return Path(sys.executable).parent
    return Path(__file__).parent


BASE_DIR = get_base_dir()

# --------------------------------------------------------------------------
# Hintergrund-Worker
# --------------------------------------------------------------------------
class BackgroundProcess:
    def __init__(
        self,
        charge: str,
        on_progress,
        on_done,
        on_error,
        on_log,
        extra_flag: Optional[str] = None,
    ):
        self.charge = charge
        self.on_progress = on_progress
        self.on_done = on_done
        self.on_error = on_error
        self.on_log = on_log
        self.extra_flag = extra_flag
        self._thread: Optional[threading.Thread] = None
        self._cancelled = threading.Event()

    # -------------------------
    def start(self) -> None:
        self._thread = threading.Thread(target=self._run, daemon=True)
        self._thread.start()

    def cancel(self) -> None:
        self._cancelled.set()

    # -------------------------
    def _run(self) -> None:
        try:
            # Arbeitsverzeichnis auf portable Ordner setzen
            os.chdir(BASE_DIR)

            # Pfad zur Orchestrator-EXE bzw. .py ermitteln
            exe_path = BASE_DIR / "sap_orchestrator.exe"
            if exe_path.exists():
                cmd = [str(exe_path), "--charge", self.charge]
            else:
                script_path = BASE_DIR / "sap_orchestrator.py"
                if not script_path.exists():
                    raise FileNotFoundError(
                        f"sap_orchestrator.exe/.py nicht gefunden in {BASE_DIR}"
                    )
                cmd = [sys.executable, str(script_path), "--charge", self.charge]

            if self.extra_flag:
                cmd.append(self.extra_flag)

            self.on_log(f"Starte: {' '.join(cmd)}\n")

            proc = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding=locale.getpreferredencoding(False),
                errors="replace",
            )

            start_time = time.time()
            timeout = 300  # s
            while True:
                if self._cancelled.is_set():
                    proc.terminate()
                    self.on_error(RuntimeError("Prozess abgebrochen"))
                    return

                if time.time() - start_time > timeout:
                    proc.terminate()
                    self.on_error(RuntimeError("Timeout erreicht"))
                    return

                line = proc.stdout.readline()
                if not line and proc.poll() is not None:
                    break

                if line:
                    self.on_log(line)
                    if line.startswith("PROGRESS "):
                        parts = line.split(" ", 2)
                        percent = int(parts[1]) if len(parts) > 1 else None
                        msg = parts[2] if len(parts) > 2 else ""
                        self.on_progress(percent, msg)
                        start_time = time.time()
                else:
                    time.sleep(0.1)

            rc = proc.wait()
            if rc == 0:
                self.on_done("Prozess beendet")
            else:
                self.on_error(RuntimeError(f"Exit-Code {rc}"))

        except Exception as exc:
            self.on_error(exc)


# --------------------------------------------------------------------------
# Tk-GUI
# --------------------------------------------------------------------------
class App(tk.Tk):
    def __init__(self) -> None:
        super().__init__()
        self.title("Charge Processor")
        self.geometry("420x300")
        self.resizable(False, False)

        self._worker: Optional[BackgroundProcess] = None
        self._indeterminate = False

        main = ttk.Frame(self, padding=16)
        main.pack(fill="both", expand=True)

        # Eingabe Charge
        ttk.Label(main, text="Charge").grid(row=0, column=0, sticky="w")
        self.var_charge = tk.StringVar()
        ttk.Entry(main, textvariable=self.var_charge, width=36).grid(
            row=1, column=0, columnspan=2, sticky="we", pady=(0, 8)
        )

        # Trockenlauf
        self.var_dry = tk.BooleanVar()
        ttk.Checkbutton(main, text="Trockenlauf (ohne SAP)", variable=self.var_dry).grid(
            row=2, column=0, columnspan=2, sticky="w", pady=(0, 8)
        )

        # Buttons
        ttk.Button(main, text="Start", command=self.on_start).grid(
            row=3, column=0, sticky="we"
        )
        ttk.Button(main, text="Abbrechen", command=self.on_cancel).grid(
            row=3, column=1, sticky="we"
        )

        # Progress + Status
        self.progress = ttk.Progressbar(main, orient="horizontal", mode="determinate")
        self.progress.grid(row=4, column=0, columnspan=2, sticky="we", pady=(8, 0))

        self.var_status = tk.StringVar()
        ttk.Label(main, textvariable=self.var_status).grid(
            row=5, column=0, columnspan=2, sticky="w"
        )

        # Log
        self.log = tk.Text(main, height=6, width=50)
        self.log.grid(row=6, column=0, columnspan=2, sticky="we", pady=(8, 0))
        scroll = ttk.Scrollbar(main, orient="vertical", command=self.log.yview)
        scroll.grid(row=6, column=2, sticky="ns")
        self.log["yscrollcommand"] = scroll.set

        main.columnconfigure(0, weight=1)
        main.columnconfigure(1, weight=1)

        self.bind("<Return>", lambda _: self.on_start())

    # ----------------- GUI-Events -----------------------------------------
    def on_start(self) -> None:
        charge = self.var_charge.get().strip()
        if not charge:
            messagebox.showwarning("Eingabe fehlt", "Bitte eine Charge eingeben.")
            return

        self._set_running(True)
        self.progress["value"] = 0
        self.var_status.set("Starte ...")
        self.log.delete(1.0, tk.END)

        self._worker = BackgroundProcess(
            charge,
            self._on_progress,
            self._on_done,
            self._on_error,
            self._on_log,
            extra_flag="--dry-run" if self.var_dry.get() else None,
        )
        self._worker.start()

    def on_cancel(self) -> None:
        if self._worker:
            self._worker.cancel()

    # ---------------- Worker-Callbacks ------------------------------------
    def _on_progress(self, percent: Optional[int], msg: str) -> None:
        if percent is None:
            if not self._indeterminate:
                self.progress.configure(mode="indeterminate")
                self.progress.start(10)
                self._indeterminate = True
        else:
            if self._indeterminate:
                self.progress.stop()
                self.progress.configure(mode="determinate")
                self._indeterminate = False
            self.progress["value"] = percent
            self.var_status.set(f"{percent}% – {msg}")

    def _on_done(self, msg: str) -> None:
        self.progress.stop()
        self.progress["value"] = 100
        self.var_status.set(msg)
        self.after(1000, lambda: self._set_running(False))

    def _on_error(self, exc: Exception) -> None:
        self.progress.stop()
        self.progress["value"] = 0
        self._set_running(False)
        messagebox.showerror("Fehler", str(exc))

    def _on_log(self, line: str) -> None:
        self.log.insert(tk.END, line)
        self.log.see(tk.END)

    # ---------------- intern ----------------------------------------------
    def _set_running(self, running: bool) -> None:
        state = "disabled" if running else "normal"
        for child in self.children.values():
            if isinstance(child, ttk.Entry) or isinstance(child, ttk.Button):
                child["state"] = state
        self.progress.configure(mode="determinate")
        self._indeterminate = False


def main() -> None:
    App().mainloop()


if __name__ == "__main__":
    main()